// Analytics Error Handler
(function() {
  // Create a wrapper for console.error to catch and handle analytics-related errors
  const originalConsoleError = console.error;
  console.error = function() {
    // Check if the error is related to analytics or tracking
    const errorString = Array.from(arguments).join(' ');
    const isAnalyticsError = 
      errorString.includes('mixpanel') || 
      errorString.includes('analytics') || 
      errorString.includes('tracking') ||
      errorString.includes('ERR_BLOCKED_BY_CLIENT');
    
    // If it's an analytics error, handle it silently in production
    if (isAnalyticsError && process.env.NODE_ENV === 'production') {
      // Optionally log to a custom error tracking service
      // that respects user privacy
      return;
    }
    
    // Otherwise, pass through to the original console.error
    return originalConsoleError.apply(console, arguments);
  };
  
  // Add a global error handler for failed network requests
  window.addEventListener('error', function(e) {
    if (e.target && e.target.tagName === 'SCRIPT') {
      const src = e.target.src || '';
      if (src.includes('mixpanel') || src.includes('analytics')) {
        // Prevent the error from bubbling up
        e.preventDefault();
        e.stopPropagation();
      }
    }
  }, true);
})();
