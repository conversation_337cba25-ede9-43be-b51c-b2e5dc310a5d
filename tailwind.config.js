/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#1A5276',
          light: '#3498DB',
          dark: '#0E2F44'
        },
        accent: {
          DEFAULT: '#D4AC0D',
          dark: '#7D6608'
        },
        background: '#F7F9F9',
        card: '#FFFFFF',
        text: {
          DEFAULT: '#333333',
          light: '#666666'
        },
        border: '#EEEEEE',
        success: '#2ECC71',
        warning: '#F39C12',
        error: '#E74C3C'
      },
      fontFamily: {
        sans: ['Segoe UI', 'Tahoma', 'Geneva', 'Verdana', 'sans-serif']
      }
    }
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms')
  ]
}
