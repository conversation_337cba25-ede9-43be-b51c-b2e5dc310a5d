/// <reference types="@sveltejs/kit" />
/// <reference no-default-lib="true"/>
/// <reference lib="esnext" />
/// <reference lib="webworker" />

import { build, files, version } from '$service-worker';

// Create a unique cache name for this deployment
const CACHE = `cache-${version}`;

const ASSETS = [
  ...build, // the app itself
  ...files  // everything in the static directory
];

// Install event - cache all static assets
self.addEventListener('install', (event) => {
  // Create a new cache and add all files to it
  async function addFilesToCache() {
    const cache = await caches.open(CACHE);
    await cache.addAll(ASSETS);
  }

  event.waitUntil(addFilesToCache());
});

// Activate event - delete old caches
self.addEventListener('activate', (event) => {
  // Delete old caches
  async function deleteOldCaches() {
    for (const key of await caches.keys()) {
      if (key !== CACHE) await caches.delete(key);
    }
  }

  event.waitUntil(deleteOldCaches());
});

// Fetch event - respond with cached assets or fetch from network
self.addEventListener('fetch', (event) => {
  // Ignore non-GET requests
  if (event.request.method !== 'GET') return;

  async function respond() {
    const url = new URL(event.request.url);
    const cache = await caches.open(CACHE);

    // For API requests, try network first, then fall back to cache
    if (url.pathname.startsWith('/api')) {
      try {
        const response = await fetch(event.request);
        
        // Cache the response if it's valid
        if (response.ok) {
          cache.put(event.request, response.clone());
        }
        
        return response;
      } catch (error) {
        // If network request fails, try to return from cache
        const cachedResponse = await cache.match(event.request);
        if (cachedResponse) return cachedResponse;
        
        // If nothing in cache, return a basic error response
        return new Response('Network error happened', {
          status: 408,
          headers: { 'Content-Type': 'text/plain' }
        });
      }
    }
    
    // For page navigation requests, try cache first, then network
    if (event.request.mode === 'navigate') {
      try {
        // Try network first for fresh content
        const response = await fetch(event.request);
        
        // Cache the response
        cache.put(event.request, response.clone());
        
        return response;
      } catch (error) {
        // If network fails, try cache
        const cachedResponse = await cache.match(event.request);
        if (cachedResponse) return cachedResponse;
        
        // If nothing in cache, try to return the index page
        const indexResponse = await cache.match('/');
        if (indexResponse) return indexResponse;
      }
    }

    // For all other requests (assets, etc.), try cache first, then network
    const cachedResponse = await cache.match(event.request);
    if (cachedResponse) return cachedResponse;

    try {
      const response = await fetch(event.request);
      
      // Cache the response if it's valid
      if (response.ok) {
        cache.put(event.request, response.clone());
      }
      
      return response;
    } catch (error) {
      // If both cache and network fail, return a basic error response
      return new Response('Network error happened', {
        status: 408,
        headers: { 'Content-Type': 'text/plain' }
      });
    }
  }

  event.respondWith(respond());
});
