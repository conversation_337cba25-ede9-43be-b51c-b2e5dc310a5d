/**
 * User model for the Food Diversity Tracker application
 * Handles user data, preferences, and subscription tier information
 */

/**
 * User subscription tiers
 */
export enum SubscriptionTiers {
  FREE = 'free',
  PAID_TIER_1 = 'paid_tier_1',
  PAID_TIER_2 = 'paid_tier_2'
}

/**
 * Food preferences interface
 */
export interface FoodPreferences {
  likes: string[];
  dislikes: string[];
  allergies: string[];
}

/**
 * User preferences interface
 */
export interface UserPreferences {
  measurementSystem: string;
  dietaryRestrictions: string[];
  location: string;
  foodPreferences: FoodPreferences;
  healthGoals: string[];
  foodPreferencesText?: string;
}

/**
 * User state interface
 */
export interface UserState {
  id: string | null;
  email: string | null;
  name: string | null;
  subscriptionTier: SubscriptionTiers;
  preferences: UserPreferences;
  weekStartDate: Date;
  isAuthenticated: boolean;
}

/**
 * Default user preferences
 */
export const defaultUserPreferences: UserPreferences = {
  measurementSystem: 'metric',
  dietaryRestrictions: [],
  location: '',
  foodPreferences: {
    likes: [],
    dislikes: [],
    allergies: []
  },
  healthGoals: []
};

/**
 * Default user state
 */
const defaultUserState: UserState = {
  id: null,
  email: null,
  name: null,
  subscriptionTier: SubscriptionTiers.FREE,
  preferences: defaultUserPreferences,
  weekStartDate: new Date(),
  isAuthenticated: false
};

/**
 * User class to manage user data and operations
 */
class User {
  state: UserState;

  constructor(userData: Partial<UserState> = {}) {
    this.state = {
      ...defaultUserState,
      ...userData
    };
  }

  /**
   * Get user information
   * @returns User state
   */
  getUserInfo(): UserState {
    return { ...this.state };
  }

  /**
   * Update user preferences
   * @param newPreferences - New user preferences
   * @returns Updated user preferences
   */
  updatePreferences(newPreferences: Partial<UserPreferences>): UserPreferences {
    this.state.preferences = {
      ...this.state.preferences,
      ...newPreferences
    };
    this.saveToLocalStorage();
    return this.state.preferences;
  }

  /**
   * Update subscription tier
   * @param tier - New subscription tier
   * @returns Updated subscription tier
   */
  updateSubscriptionTier(tier: SubscriptionTiers): SubscriptionTiers {
    try {
      // Validate the tier is a valid subscription tier
      if (!Object.values(SubscriptionTiers).includes(tier)) {
        console.warn(`Invalid subscription tier: ${tier}. Using current tier.`);
        return this.state.subscriptionTier;
      }
      
      this.state.subscriptionTier = tier;
      this.saveToLocalStorage();
      return this.state.subscriptionTier;
    } catch (error) {
      console.error('Error updating subscription tier:', error);
      return this.state.subscriptionTier;
    }
  }

  /**
   * Check if a feature is available for the user's subscription tier
   * @param feature - Feature to check
   * @returns Whether the feature is available
   */
  hasFeatureAccess(feature: string): boolean {
    const tierFeatures: Record<SubscriptionTiers, string[]> = {
      [SubscriptionTiers.FREE]: [
        'basic_tracking',
        'standard_food_list',
        'basic_export',
        'basic_groceries',
        'sample_meal_plan'
      ],
      [SubscriptionTiers.PAID_TIER_1]: [
        'basic_tracking',
        'standard_food_list',
        'basic_export',
        'basic_groceries',
        'sample_meal_plan',
        'personalized_food_list',
        'custom_groceries',
        'detailed_meal_plan',
        'shared_shopping_list',
        'animated_ui' // Animations for paid tier 1
      ],
      [SubscriptionTiers.PAID_TIER_2]: [
        'basic_tracking',
        'standard_food_list',
        'basic_export',
        'basic_groceries',
        'sample_meal_plan',
        'personalized_food_list',
        'custom_groceries',
        'detailed_meal_plan',
        'shared_shopping_list',
        'user_profile',
        'gamification',
        'advanced_analytics',
        'advanced_personalization',
        'animated_ui', // Animations for paid tier 2
        'advanced_animations' // Additional premium animations
      ]
    };

    return tierFeatures[this.state.subscriptionTier]?.includes(feature) || false;
  }

  /**
   * Save user state to local storage
   */
  saveToLocalStorage(): void {
    try {
      // Create a copy of the state to avoid circular references
      const stateCopy = JSON.parse(JSON.stringify(this.state));
      const serializedState = JSON.stringify(stateCopy);
      localStorage.setItem('foodDiversityUser', serializedState);
    } catch (error) {
      console.error('Error saving user state to local storage:', error);
    }
  }

  /**
   * Load user state from local storage
   * @returns User instance
   */
  static loadFromLocalStorage(): User {
    try {
      if (typeof localStorage !== 'undefined') {
        const serializedState = localStorage.getItem('foodDiversityUser');
        if (serializedState) {
          const userData = JSON.parse(serializedState);
          // Convert string date back to Date object
          if (userData.weekStartDate) {
            userData.weekStartDate = new Date(userData.weekStartDate);
          }
          return new User(userData);
        }
      }
    } catch (error) {
      console.error('Error loading user state from local storage:', error);
    }
    return new User();
  }
}

export default User;
