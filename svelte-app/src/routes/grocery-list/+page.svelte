<script lang="ts">
  import { onMount } from 'svelte';
  import { 
    generateWeeklyFoodList, 
    generateGroceryList,
    type WeeklyFoodList, 
    type ConsumedFoods,
    type FoodItem
  } from '$lib/services/foodService';
  
  // State
  let weeklyFoodList: WeeklyFoodList | null = null;
  let consumedFoods: ConsumedFoods = {};
  let groceryList: Record<string, FoodItem[]> = {};
  let isLoading = true;
  let error: string | null = null;
  
  // Initialize data
  onMount(async () => {
    try {
      // Load food list
      const response = await generateWeeklyFoodList();
      weeklyFoodList = response.weeklyFoodList;
      
      // Load consumed foods from localStorage
      try {
        const savedConsumedFoods = localStorage.getItem('consumedFoods');
        if (savedConsumedFoods) {
          consumedFoods = JSON.parse(savedConsumedFoods);
        }
      } catch (err) {
        console.error('Error loading consumed foods from localStorage:', err);
      }
      
      // Generate grocery list
      if (weeklyFoodList) {
        groceryList = generateGroceryList(weeklyFoodList, consumedFoods);
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error in grocery list page:', err);
    } finally {
      isLoading = false;
    }
  });
  
  // Count total items in grocery list
  $: totalItems = Object.values(groceryList).reduce((total, items) => total + items.length, 0);
</script>

<div class="container mx-auto px-4 py-8">
  <div class="mb-8 text-center">
    <h1 class="text-3xl font-bold mb-4">Grocery List</h1>
    <p class="text-lg mb-6">Items you haven't consumed yet this week.</p>
  </div>
  
  {#if isLoading}
    <div class="text-center py-12">
      <div class="inline-block w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      <p class="mt-4 text-lg">Generating your grocery list...</p>
    </div>
  {:else if error}
    <div class="bg-red-50 border border-red-200 text-red-700 p-6 rounded-lg text-center">
      <div class="text-3xl mb-4">⚠️</div>
      <h2 class="text-xl font-bold mb-2">Error Generating Grocery List</h2>
      <p>{error}</p>
      <button 
        class="mt-4 bg-primary text-white px-4 py-2 rounded hover:bg-primary-dark transition-colors"
        on:click={() => window.location.reload()}
      >
        Try Again
      </button>
    </div>
  {:else if totalItems === 0}
    <div class="text-center py-8 card">
      <div class="text-5xl mb-4">🎉</div>
      <h2 class="text-2xl font-bold mb-2">All Items Consumed!</h2>
      <p>You've consumed all the items on your food list this week. Great job!</p>
      <a href="/tracker" class="btn-primary inline-block mt-4">Back to Tracker</a>
    </div>
  {:else}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {#each Object.entries(groceryList) as [category, items]}
        {#if items.length > 0}
          <div class="card">
            <h2 class="text-xl font-bold mb-4 pb-2 border-b capitalize">
              {category.replace(/([A-Z])/g, ' $1').trim()}
            </h2>
            
            <ul class="space-y-2">
              {#each items as item}
                <li class="flex items-center p-2 border-b border-gray-100 last:border-0">
                  <span class="w-5 h-5 inline-flex items-center justify-center bg-primary text-white rounded-full text-xs mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    </svg>
                  </span>
                  <div>
                    <div>{item.name}</div>
                    {#if item.servingSize}
                      <div class="text-xs text-gray-500">
                        {item.servingSize.metric} / {item.servingSize.imperial}
                      </div>
                    {/if}
                  </div>
                </li>
              {/each}
            </ul>
          </div>
        {/if}
      {/each}
    </div>
    
    <div class="mt-8 text-center">
      <button 
        class="btn-primary"
        on:click={() => window.print()}
      >
        Print Grocery List
      </button>
    </div>
  {/if}
</div>
