<script lang="ts">
  import { onMount } from 'svelte';
  import { 
    generateWeeklyFoodList, 
    formatDataForExport,
    type WeeklyFoodList, 
    type ConsumedFoods
  } from '$lib/services/foodService';
  import { userPreferences } from '$lib/stores/userStore';
  
  // State
  let weeklyFoodList: WeeklyFoodList | null = null;
  let consumedFoods: ConsumedFoods = {};
  let exportData: any = null;
  let isLoading = true;
  let error: string | null = null;
  
  // Initialize data
  onMount(async () => {
    try {
      // Load food list
      const response = await generateWeeklyFoodList();
      weeklyFoodList = response.weeklyFoodList;
      
      // Load consumed foods from localStorage
      try {
        const savedConsumedFoods = localStorage.getItem('consumedFoods');
        if (savedConsumedFoods) {
          consumedFoods = JSON.parse(savedConsumedFoods);
        }
      } catch (err) {
        console.error('Error loading consumed foods from localStorage:', err);
      }
      
      // Format data for export
      if (weeklyFoodList) {
        exportData = formatDataForExport(weeklyFoodList, consumedFoods, $userPreferences);
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error in export page:', err);
    } finally {
      isLoading = false;
    }
  });
  
  // Export as JSON
  function exportAsJSON() {
    if (!exportData) return;
    
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
    
    const exportFileDefaultName = `food-diversity-tracker-${new Date().toISOString().slice(0, 10)}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  }
  
  // Export as Markdown
  function exportAsMarkdown() {
    if (!exportData) return;
    
    let markdown = `# Food Diversity Tracker Export\n\n`;
    markdown += `Date: ${new Date().toLocaleDateString()}\n\n`;
    
    markdown += `## Food Categories\n\n`;
    
    Object.entries(exportData.weeklyFoodList).forEach(([category, data]: [string, any]) => {
      markdown += `### ${category.charAt(0).toUpperCase() + category.slice(1)}\n\n`;
      
      markdown += `**Core Items:**\n\n`;
      data.coreItems.forEach((item: string) => {
        markdown += `- ${item}\n`;
      });
      
      markdown += `\n**Additional Options:**\n\n`;
      data.additionalOptions.forEach((item: string) => {
        markdown += `- ${item}\n`;
      });
      
      markdown += `\n`;
    });
    
    markdown += `## Nutrition Notes\n\n${exportData.nutritionNotes || 'No nutrition notes available.'}\n\n`;
    markdown += `## Seasonal Considerations\n\n${exportData.seasonalConsiderations || 'No seasonal considerations available.'}\n\n`;
    
    markdown += `## Meal Plan Suggestions\n\n`;
    
    Object.entries(exportData.mealPlanSuggestions).forEach(([mealType, suggestions]: [string, any]) => {
      markdown += `### ${mealType.charAt(0).toUpperCase() + mealType.slice(1)}\n\n`;
      
      suggestions.forEach((suggestion: string) => {
        markdown += `- ${suggestion}\n`;
      });
      
      markdown += `\n`;
    });
    
    const dataUri = `data:text/markdown;charset=utf-8,${encodeURIComponent(markdown)}`;
    
    const exportFileDefaultName = `food-diversity-tracker-${new Date().toISOString().slice(0, 10)}.md`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  }
</script>

<div class="container mx-auto px-4 py-8">
  <div class="mb-8 text-center">
    <h1 class="text-3xl font-bold mb-4">Export Data</h1>
    <p class="text-lg mb-6">Export your food tracking data in different formats.</p>
  </div>
  
  {#if isLoading}
    <div class="text-center py-12">
      <div class="inline-block w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      <p class="mt-4 text-lg">Preparing your data for export...</p>
    </div>
  {:else if error}
    <div class="bg-red-50 border border-red-200 text-red-700 p-6 rounded-lg text-center">
      <div class="text-3xl mb-4">⚠️</div>
      <h2 class="text-xl font-bold mb-2">Error Preparing Export</h2>
      <p>{error}</p>
      <button 
        class="mt-4 bg-primary text-white px-4 py-2 rounded hover:bg-primary-dark transition-colors"
        on:click={() => window.location.reload()}
      >
        Try Again
      </button>
    </div>
  {:else if exportData}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <div class="card p-6 text-center">
        <div class="text-4xl mb-4">📄</div>
        <h3 class="text-xl font-bold mb-2">JSON Format</h3>
        <p class="mb-4">Export your data in JSON format for use in other applications.</p>
        <button 
          class="btn-primary w-full"
          on:click={exportAsJSON}
        >
          Export as JSON
        </button>
      </div>
      
      <div class="card p-6 text-center">
        <div class="text-4xl mb-4">📝</div>
        <h3 class="text-xl font-bold mb-2">Markdown Format</h3>
        <p class="mb-4">Export your data in Markdown format for easy reading and sharing.</p>
        <button 
          class="btn-primary w-full"
          on:click={exportAsMarkdown}
        >
          Export as Markdown
        </button>
      </div>
      
      <div class="card p-6 text-center">
        <div class="text-4xl mb-4">🖨️</div>
        <h3 class="text-xl font-bold mb-2">Print</h3>
        <p class="mb-4">Print your food tracking data directly from your browser.</p>
        <button 
          class="btn-primary w-full"
          on:click={() => window.print()}
        >
          Print Data
        </button>
      </div>
    </div>
    
    <div class="card p-6">
      <h2 class="text-2xl font-bold mb-4">Preview</h2>
      
      <div class="prose max-w-none">
        <h3>Food Categories</h3>
        
        {#each Object.entries(exportData.weeklyFoodList) as [category, data]}
          <h4 class="capitalize">{category.replace(/([A-Z])/g, ' $1').trim()}</h4>
          
          <p><strong>Core Items:</strong></p>
          <ul>
            {#each data.coreItems as item}
              <li>{item}</li>
            {/each}
          </ul>
          
          <p><strong>Additional Options:</strong></p>
          <ul>
            {#each data.additionalOptions as item}
              <li>{item}</li>
            {/each}
          </ul>
        {/each}
        
        <h3>Nutrition Notes</h3>
        <p>{exportData.nutritionNotes || 'No nutrition notes available.'}</p>
        
        <h3>Seasonal Considerations</h3>
        <p>{exportData.seasonalConsiderations || 'No seasonal considerations available.'}</p>
        
        <h3>Meal Plan Suggestions</h3>
        
        {#each Object.entries(exportData.mealPlanSuggestions) as [mealType, suggestions]}
          <h4 class="capitalize">{mealType}</h4>
          <ul>
            {#each suggestions as suggestion}
              <li>{suggestion}</li>
            {/each}
          </ul>
        {/each}
      </div>
    </div>
  {:else}
    <div class="text-center py-8">
      <p>No data available for export. Start tracking your food consumption first.</p>
      <a href="/tracker" class="btn-primary inline-block mt-4">Go to Food Tracker</a>
    </div>
  {/if}
</div>
