<script lang="ts">
	import '../app.css';
	import Header from '$lib/components/Header.svelte';
	import Footer from '$lib/components/Footer.svelte';
	import NetworkStatus from '$lib/components/NetworkStatus.svelte';
	import NetworkDetector from '$lib/components/NetworkDetector.svelte';
	import { onMount } from 'svelte';
	import { userStore } from '$lib/stores/userStore';
	import { registerServiceWorker } from '$lib/utils/registerServiceWorker';
	import { setupNetworkListeners, getSyncStatus } from '$lib/services/syncService';
	import { registerBackgroundSync } from '$lib/utils/indexedDB';
	import { writable } from 'svelte/store';

	// Create a store for network status
	export const networkStatus = writable({
		online: typeof navigator !== 'undefined' ? navigator.onLine : true,
		pendingSyncs: 0
	});

	let { children } = $props();
	let isLoaded = $state(false);

	// Initialize the user store and register service worker on mount
	onMount(() => {
		// Initialize user store
		userStore.initialize();

		// Register service worker
		registerServiceWorker();

		// Setup network listeners for online/offline events
		setupNetworkListeners();

		// Register for background sync
		registerBackgroundSync()
			.then(() => {
				console.log('Background sync registered successfully');
			})
			.catch((error) => {
				console.warn('Background sync registration failed:', error);
			});

		// Update network status periodically
		const updateNetworkStatus = () => {
			getSyncStatus()
				.then(({ online, pendingCount }) => {
					networkStatus.set({
						online,
						pendingSyncs: pendingCount
					});
				})
				.catch((error) => {
					console.error('Error updating network status:', error);
				});
		};

		// Initial update
		updateNetworkStatus();

		// Set up periodic updates
		const intervalId = setInterval(updateNetworkStatus, 30000); // Check every 30 seconds

		// Set up service worker message listener for notification clicks
		if ('serviceWorker' in navigator) {
			navigator.serviceWorker.addEventListener('message', (event) => {
				const data = event.data;

				// Handle notification click messages
				if (data && data.type === 'NOTIFICATION_CLICKED') {
					console.log('Notification clicked:', data);

					// Record the tracking time for adaptive notifications
					if (data.mealType) {
						import('$lib/services/notificationService')
							.then(({ recordTrackingTime }) => {
								recordTrackingTime(data.mealType);
							})
							.catch(error => {
								console.error('Error importing notification service:', error);
							});
					}
				}
			});
		}

		// Mark as loaded after a short delay to ensure styles are applied
		setTimeout(() => {
			isLoaded = true;
		}, 100);

		// Clean up on component unmount
		return () => {
			clearInterval(intervalId);
		};
	});
</script>

<div class="flex flex-col min-h-screen bg-background">
	<Header />

	<main class="flex-grow">
		{#if isLoaded}
			{@render children()}
		{:else}
			<div class="flex items-center justify-center min-h-screen">
				<div class="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
			</div>
		{/if}
	</main>

	<Footer />

	<!-- Network status indicator -->
	<NetworkStatus />

	<!-- Network detector for offline/online notifications -->
	<NetworkDetector />
</div>

<!-- PWA manifest and icons -->
<svelte:head>
	<link rel="manifest" href="/manifest.json">
	<meta name="theme-color" content="#1A5276">
	<link rel="apple-touch-icon" href="/icons/icon-192x192.png">
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous">
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</svelte:head>
