:root {
  /* Color palette */
  --primary-color: #1A5276;
  --secondary-color: #3498DB;
  --accent-color: #D4AC0D;
  --accent-dark: #7D6608;
  --background-color: #F7F9F9;
  --card-background: #FFFFFF;
  --text-color: #333333;
  --text-light: #666666;
  --border-color: #EEEEEE;
  --success-color: #2ECC71;
  --warning-color: #F39C12;
  --error-color: #E74C3C;

  /* Typography */
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Other */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
}

body {
  margin: 0;
  background-color: var(--background-color);
  color: var(--text-color);
}

a {
  color: var(--secondary-color);
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: var(--primary-color);
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.5rem;
}

p {
  margin-bottom: 1rem;
}

button {
  cursor: pointer;
  font-family: inherit;
  border: none;
  background: none;
}

#app {
  width: 100%;
  height: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.card {
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.btn {
  display: inline-block;
  padding: 0.5rem 1.5rem;
  border-radius: 4px;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.2s;
}

.btn:active {
  transform: translateY(1px);
}

.btn-primary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-color);
}

.btn-secondary {
  background-color: var(--background-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--border-color);
}
