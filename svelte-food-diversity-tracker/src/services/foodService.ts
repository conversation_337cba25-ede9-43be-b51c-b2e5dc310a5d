import type { UserPreferences } from '../models/userModel';

// Define interfaces for food data
export interface FoodItem {
  id: string;
  name: string;
  servingSize?: {
    metric: string;
    imperial: string;
  };
}

export interface FoodCategory {
  coreItems: FoodItem[];
  additionalOptions: FoodItem[];
}

export interface WeeklyFoodList {
  [category: string]: FoodCategory;
}

export interface FoodListResponse {
  weeklyFoodList: WeeklyFoodList;
  nutritionNotes?: string;
  seasonalConsiderations?: string;
  measurementSystem?: string;
}

export interface ProgressStats {
  totalPercentage: number;
  categoryPercentages: {
    [category: string]: {
      percentage: number;
      consumedCount: number;
      totalCount: number;
    };
  };
  consumedCount: number;
  totalCount: number;
}

export interface ConsumedFoods {
  [foodId: string]: boolean;
}

// Mock data for development
const defaultFoodList: FoodListResponse = {
  weeklyFoodList: {
    vegetables: {
      coreItems: [
        { id: 'v1', name: 'Leafy Greens', servingSize: { metric: '100g', imperial: '3.5oz' } },
        { id: 'v2', name: 'Bell Peppers', servingSize: { metric: '100g', imperial: '3.5oz' } },
        { id: 'v3', name: '<PERSON><PERSON><PERSON><PERSON>', servingSize: { metric: '100g', imperial: '3.5oz' } },
        { id: 'v4', name: 'Carrots', servingSize: { metric: '100g', imperial: '3.5oz' } },
        { id: 'v5', name: 'Tomatoes', servingSize: { metric: '100g', imperial: '3.5oz' } }
      ],
      additionalOptions: [
        { id: 'v6', name: 'Cucumber', servingSize: { metric: '100g', imperial: '3.5oz' } },
        { id: 'v7', name: 'Zucchini', servingSize: { metric: '100g', imperial: '3.5oz' } }
      ]
    },
    fruits: {
      coreItems: [
        { id: 'f1', name: 'Berries', servingSize: { metric: '100g', imperial: '3.5oz' } },
        { id: 'f2', name: 'Citrus Fruits', servingSize: { metric: '1 medium', imperial: '1 medium' } },
        { id: 'f3', name: 'Apples', servingSize: { metric: '1 medium', imperial: '1 medium' } }
      ],
      additionalOptions: [
        { id: 'f4', name: 'Bananas', servingSize: { metric: '1 medium', imperial: '1 medium' } },
        { id: 'f5', name: 'Grapes', servingSize: { metric: '100g', imperial: '3.5oz' } }
      ]
    },
    healthyFats: {
      coreItems: [
        { id: 'h1', name: 'Olive Oil', servingSize: { metric: '15ml', imperial: '1 tbsp' } },
        { id: 'h2', name: 'Avocados', servingSize: { metric: '1/2 medium', imperial: '1/2 medium' } },
        { id: 'h3', name: 'Nuts', servingSize: { metric: '30g', imperial: '1oz' } }
      ],
      additionalOptions: [
        { id: 'h4', name: 'Seeds', servingSize: { metric: '15g', imperial: '0.5oz' } },
        { id: 'h5', name: 'Coconut Oil', servingSize: { metric: '15ml', imperial: '1 tbsp' } }
      ]
    },
    proteinSources: {
      coreItems: [
        { id: 'p1', name: 'Fish', servingSize: { metric: '100g', imperial: '3.5oz' } },
        { id: 'p2', name: 'Eggs', servingSize: { metric: '2 large', imperial: '2 large' } },
        { id: 'p3', name: 'Poultry', servingSize: { metric: '100g', imperial: '3.5oz' } }
      ],
      additionalOptions: [
        { id: 'p4', name: 'Beef', servingSize: { metric: '100g', imperial: '3.5oz' } },
        { id: 'p5', name: 'Lamb', servingSize: { metric: '100g', imperial: '3.5oz' } }
      ]
    },
    fermentedFoods: {
      coreItems: [
        { id: 'ff1', name: 'Yogurt', servingSize: { metric: '150g', imperial: '5oz' } },
        { id: 'ff2', name: 'Sauerkraut', servingSize: { metric: '30g', imperial: '1oz' } }
      ],
      additionalOptions: [
        { id: 'ff3', name: 'Kefir', servingSize: { metric: '150ml', imperial: '5oz' } },
        { id: 'ff4', name: 'Kimchi', servingSize: { metric: '30g', imperial: '1oz' } }
      ]
    },
    grainsAndLegumes: {
      coreItems: [
        { id: 'gl1', name: 'Quinoa', servingSize: { metric: '50g dry', imperial: '1/4 cup dry' } },
        { id: 'gl2', name: 'Lentils', servingSize: { metric: '50g dry', imperial: '1/4 cup dry' } }
      ],
      additionalOptions: [
        { id: 'gl3', name: 'Brown Rice', servingSize: { metric: '50g dry', imperial: '1/4 cup dry' } },
        { id: 'gl4', name: 'Chickpeas', servingSize: { metric: '50g dry', imperial: '1/4 cup dry' } }
      ]
    },
    herbsSpicesCondiments: {
      coreItems: [
        { id: 'hsc1', name: 'Garlic', servingSize: { metric: '2 cloves', imperial: '2 cloves' } },
        { id: 'hsc2', name: 'Turmeric', servingSize: { metric: '1g', imperial: '1/4 tsp' } },
        { id: 'hsc3', name: 'Cinnamon', servingSize: { metric: '1g', imperial: '1/4 tsp' } }
      ],
      additionalOptions: [
        { id: 'hsc4', name: 'Ginger', servingSize: { metric: '5g', imperial: '1 tsp grated' } },
        { id: 'hsc5', name: 'Oregano', servingSize: { metric: '1g', imperial: '1/4 tsp' } }
      ]
    }
  },
  nutritionNotes: "This food list provides a balanced mix of nutrients following LCHF/Mediterranean principles.",
  seasonalConsiderations: "Consider local seasonal availability for the freshest produce.",
  measurementSystem: "both"
};

// Mock meal plan suggestions
const mealPlanSuggestions = {
  breakfast: [
    "Greek yogurt with berries and nuts",
    "Vegetable omelet with avocado",
    "Chia seed pudding with coconut milk"
  ],
  lunch: [
    "Mediterranean salad with olive oil dressing",
    "Grilled fish with roasted vegetables",
    "Lentil soup with leafy greens"
  ],
  dinner: [
    "Baked chicken with herbs and vegetable medley",
    "Salmon with quinoa and steamed broccoli",
    "Vegetable stir-fry with tofu or chicken"
  ],
  snacks: [
    "Handful of mixed nuts",
    "Sliced vegetables with hummus",
    "Greek yogurt with cinnamon"
  ]
};

/**
 * Generates a weekly food list based on user preferences
 * @param userPreferences - User preferences including measurement system, dietary restrictions, location
 * @returns Weekly food list with core items and additional options
 */
export const generateWeeklyFoodList = async (userPreferences: Partial<UserPreferences> = {}): Promise<FoodListResponse> => {
  try {
    // For now, we'll just return the default food list
    // In a real implementation, this would call the AI service or use cached data
    console.log('Generating weekly food list with preferences:', userPreferences);
    return defaultFoodList;
  } catch (error) {
    console.error('Error generating weekly food list:', error);
    return defaultFoodList;
  }
};

/**
 * Calculates the progress of consumed foods
 * @param weeklyFoodList - The weekly food list
 * @param consumedFoods - Object tracking which foods have been consumed
 * @returns Progress statistics
 */
export const calculateProgress = (weeklyFoodList: WeeklyFoodList, consumedFoods: ConsumedFoods): ProgressStats => {
  // Default return object for error cases
  const defaultProgress: ProgressStats = {
    totalPercentage: 0,
    categoryPercentages: {},
    consumedCount: 0,
    totalCount: 0
  };

  // Validate input parameters
  if (!weeklyFoodList || typeof weeklyFoodList !== 'object' || !consumedFoods) {
    console.warn('Invalid parameters passed to calculateProgress');
    return defaultProgress;
  }

  let consumedCount = 0;
  let totalCount = 0;
  const categoryPercentages: ProgressStats['categoryPercentages'] = {};

  try {
    // Calculate progress for each category
    Object.keys(weeklyFoodList).forEach(category => {
      // Validate category and coreItems exist
      if (!weeklyFoodList[category] || !weeklyFoodList[category].coreItems) {
        console.warn(`Category ${category} is missing or has no coreItems`);
        categoryPercentages[category] = {
          percentage: 0,
          consumedCount: 0,
          totalCount: 0
        };
        return; // Skip this category
      }

      const categoryItems = weeklyFoodList[category].coreItems;
      
      // Ensure categoryItems is an array
      if (!Array.isArray(categoryItems)) {
        console.warn(`Category ${category} has invalid coreItems (not an array)`);
        categoryPercentages[category] = {
          percentage: 0,
          consumedCount: 0,
          totalCount: 0
        };
        return; // Skip this category
      }
      
      let categoryConsumedCount = 0;
      
      // Process each item in the category
      categoryItems.forEach(item => {
        // Validate item exists and has an id
        if (item && item.id && consumedFoods[item.id]) {
          consumedCount++;
          categoryConsumedCount++;
        }
      });
      
      totalCount += categoryItems.length;
      categoryPercentages[category] = {
        percentage: categoryItems.length > 0 
          ? (categoryConsumedCount / categoryItems.length) * 100 
          : 0,
        consumedCount: categoryConsumedCount,
        totalCount: categoryItems.length
      };
    });
  } catch (error) {
    console.error('Error calculating progress:', error);
    return defaultProgress;
  }

  return {
    totalPercentage: totalCount > 0 ? (consumedCount / totalCount) * 100 : 0,
    categoryPercentages,
    consumedCount,
    totalCount
  };
};

/**
 * Generates a grocery list based on the weekly food list and consumed foods
 * @param weeklyFoodList - The weekly food list
 * @param consumedFoods - Object tracking which foods have been consumed
 * @returns Grocery list categorized by food groups
 */
export const generateGroceryList = (weeklyFoodList: WeeklyFoodList, consumedFoods: ConsumedFoods): Record<string, FoodItem[]> => {
  // Validate input parameters
  if (!weeklyFoodList || typeof weeklyFoodList !== 'object' || !consumedFoods) {
    console.warn('Invalid parameters passed to generateGroceryList');
    return {};
  }

  const groceryList: Record<string, FoodItem[]> = {};

  try {
    Object.keys(weeklyFoodList).forEach(category => {
      // Validate category and coreItems exist
      if (!weeklyFoodList[category] || !weeklyFoodList[category].coreItems) {
        console.warn(`Category ${category} is missing or has no coreItems`);
        groceryList[category] = [];
        return; // Skip this category
      }

      const categoryItems = weeklyFoodList[category].coreItems;
      
      // Ensure categoryItems is an array
      if (!Array.isArray(categoryItems)) {
        console.warn(`Category ${category} has invalid coreItems (not an array)`);
        groceryList[category] = [];
        return; // Skip this category
      }
      
      groceryList[category] = [];
      
      categoryItems.forEach(item => {
        // Validate item exists and has an id
        if (item && item.id && !consumedFoods[item.id]) {
          // Add items that haven't been consumed yet to the grocery list
          groceryList[category].push(item);
        }
      });
    });
  } catch (error) {
    console.error('Error generating grocery list:', error);
    return {};
  }

  return groceryList;
};

/**
 * Generates meal plan suggestions based on the weekly food list
 * @returns Meal plan suggestions
 */
export const generateMealPlanSuggestions = () => {
  // For MVP, we'll return the static meal plan suggestions
  return mealPlanSuggestions;
};

/**
 * Formats the food data for export
 * @param weeklyFoodList - The weekly food list
 * @param consumedFoods - Object tracking which foods have been consumed
 * @param userPreferences - User preferences
 * @returns Formatted data for export
 */
export const formatDataForExport = (
  weeklyFoodList: WeeklyFoodList, 
  consumedFoods: ConsumedFoods, 
  userPreferences: Partial<UserPreferences>
) => {
  if (!weeklyFoodList || !consumedFoods) {
    return null;
  }

  const formattedData = {
    weeklyFoodList: {} as Record<string, { coreItems: string[], additionalOptions: string[] }>,
    nutritionNotes: defaultFoodList.nutritionNotes,
    seasonalConsiderations: defaultFoodList.seasonalConsiderations,
    measurementSystem: userPreferences?.measurementSystem || 'metric',
    mealPlanSuggestions
  };

  // Format the food list data
  Object.keys(weeklyFoodList).forEach(category => {
    formattedData.weeklyFoodList[category] = {
      coreItems: weeklyFoodList[category].coreItems.map(item => item.name),
      additionalOptions: weeklyFoodList[category].additionalOptions.map(item => item.name)
    };
  });

  return formattedData;
};
