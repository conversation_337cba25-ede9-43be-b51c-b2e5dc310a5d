import { writable, derived, get } from 'svelte/store';
import User, { SubscriptionTiers, defaultUserPreferences } from '../models/userModel';
import type { UserPreferences, UserState } from '../models/userModel';

// Create a writable store for the user
function createUserStore() {
  const { subscribe, set, update } = writable<User | null>(null);

  return {
    subscribe,
    initialize: () => {
      try {
        const loadedUser = User.loadFromLocalStorage();
        set(loadedUser);
      } catch (error) {
        console.error('Error initializing user:', error);
        set(new User());
      }
    },
    updatePreferences: (newPreferences: Partial<UserPreferences>) => {
      update(user => {
        if (!user) return new User();
        const updatedPreferences = user.updatePreferences(newPreferences);
        return new User({ ...user.getUserInfo(), preferences: updatedPreferences });
      });
    },
    updateSubscriptionTier: (tier: SubscriptionTiers) => {
      update(user => {
        if (!user) return new User();
        const updatedTier = user.updateSubscriptionTier(tier);
        return new User({ ...user.getUserInfo(), subscriptionTier: updatedTier });
      });
    },
    // For development purposes
    toggleBypassSubscription: () => {
      update(user => {
        if (!user) return new User();
        return user;
      });
    }
  };
}

// Create the user store
export const userStore = createUserStore();

// Derived stores for user information
export const userInfo = derived(userStore, $userStore => $userStore?.getUserInfo() || {} as UserState);
export const subscriptionTier = derived(userInfo, $userInfo => $userInfo.subscriptionTier || SubscriptionTiers.FREE);
export const userPreferences = derived(userInfo, $userInfo => $userInfo.preferences || defaultUserPreferences);

// Feature access check function
export function hasFeatureAccess(feature: string): boolean {
  const user = get(userStore);
  if (!user) return false;
  return user.hasFeatureAccess(feature);
}

// Get user preferences function
export function getPreferences(): UserPreferences {
  const user = get(userStore);
  if (!user) return defaultUserPreferences;
  return user.getUserInfo().preferences || defaultUserPreferences;
}

// Get subscription tier function
export function getSubscriptionTier(): SubscriptionTiers {
  const user = get(userStore);
  if (!user) return SubscriptionTiers.FREE;
  return user.getUserInfo().subscriptionTier || SubscriptionTiers.FREE;
}

// Development mode settings
export const devMode = writable(true);
export const bypassSubscription = writable(false);
