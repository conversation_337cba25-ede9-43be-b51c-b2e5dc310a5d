<script lang="ts">
  import { onMount } from 'svelte';
  import Router from 'svelte-spa-router';
  import routes from './routes';
  import { userStore, hasFeatureAccess } from './stores/userStore';
  import { generateWeeklyFoodList, type WeeklyFoodList, type ConsumedFoods } from './services/foodService';

  // Application state
  let weeklyFoodList: WeeklyFoodList | null = null;
  let consumedFoods: ConsumedFoods = {};
  let isLoading = true;
  let apiError = false;
  let errorMessage = '';

  // Feature flag for animated components
  $: useAnimatedComponents = hasFeatureAccess('animated_ui') || true;

  // Initialize the application
  onMount(async () => {
    try {
      // Initialize user store
      userStore.initialize();

      // Load food list
      await loadFoodList();
    } catch (error) {
      console.error('Error initializing app:', error);
      apiError = true;
      errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    } finally {
      isLoading = false;
    }
  });

  // Load the food list
  async function loadFoodList() {
    isLoading = true;
    try {
      const foodListResponse = await generateWeeklyFoodList();

      if (!foodListResponse || !foodListResponse.weeklyFoodList) {
        throw new Error('Failed to generate food list');
      }

      weeklyFoodList = foodListResponse.weeklyFoodList;

      // Initialize consumed foods tracking
      const initialConsumedFoods: ConsumedFoods = {};

      Object.keys(weeklyFoodList || {}).forEach(category => {
        const categoryData = weeklyFoodList?.[category];
        if (categoryData && Array.isArray(categoryData.coreItems)) {
          categoryData.coreItems.forEach(item => {
            if (item && item.id) {
              initialConsumedFoods[item.id] = false;
            }
          });
        }
      });

      consumedFoods = initialConsumedFoods;
      console.log(`Initialized tracking for ${Object.keys(initialConsumedFoods).length} food items`);
    } catch (error) {
      console.error('Error loading food list:', error);
      apiError = true;
      errorMessage = error instanceof Error ? error.message : 'Failed to load food data';
    } finally {
      isLoading = false;
    }
  }

  // Toggle food consumption status
  function toggleFoodConsumed(foodId: string) {
    consumedFoods = {
      ...consumedFoods,
      [foodId]: !consumedFoods[foodId]
    };
  }
</script>

<main>
  <div class="app-container">
    <header class="app-header">
      <h1>Food Diversity Tracker</h1>
      <nav>
        <a href="#/">Home</a>
        <a href="#/tracker">Food Tracker</a>
        <a href="#/progress">Progress</a>
        <a href="#/grocery-list">Grocery List</a>
      </nav>
    </header>

    {#if isLoading}
      <div class="container">
        <div class="loading-container">
          <div class="loading-spinner"></div>
          <h2>Loading your personalized food list...</h2>
          <p>Please wait while we prepare your food diversity tracker.</p>
        </div>
      </div>
    {:else if apiError}
      <div class="container">
        <div class="error-container">
          <div class="error-icon">⚠️</div>
          <h2>Oops! Something went wrong</h2>
          <p>{errorMessage}</p>
          <p>Don't worry, we've loaded some sample food data for you.</p>
          <button
            class="btn btn-primary"
            on:click={() => window.location.reload()}
          >
            Try Again
          </button>
        </div>
      </div>
    {:else}
      <div class="content">
        <Router {routes} />
      </div>
    {/if}

    <footer class="app-footer">
      <p>&copy; 2025 Food Diversity Tracker</p>
    </footer>
  </div>
</main>

<style>
  main {
    font-family: Arial, sans-serif;
    color: #333;
    line-height: 1.6;
  }

  .app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .app-header {
    background-color: #1A5276;
    color: white;
    padding: 1rem;
    text-align: center;
  }

  .app-header h1 {
    margin: 0;
    font-size: 1.8rem;
  }

  .app-header nav {
    margin-top: 1rem;
  }

  .app-header nav a {
    color: white;
    text-decoration: none;
    margin: 0 1rem;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.3s;
  }

  .app-header nav a:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
  }

  .content {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
  }

  .landing-page {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  .food-categories {
    margin: 2rem 0;
    text-align: left;
    background-color: #f7f9f9;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .food-categories ul {
    list-style-type: none;
    padding: 0;
  }

  .food-categories li {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-bottom: 1px solid #eee;
  }

  .cta-buttons {
    margin: 2rem 0;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
  }

  .loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid #3498db;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    background-color: #fff3f3;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .btn {
    display: inline-block;
    padding: 0.5rem 1.5rem;
    margin: 0 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    text-decoration: none;
    transition: background-color 0.3s;
  }

  .btn-primary {
    background-color: #3498db;
    color: white;
  }

  .btn-primary:hover {
    background-color: #2980b9;
  }

  .btn-secondary {
    background-color: #f7f9f9;
    color: #333;
    border: 1px solid #ddd;
  }

  .btn-secondary:hover {
    background-color: #eee;
  }

  .app-footer {
    background-color: #1A5276;
    color: white;
    text-align: center;
    padding: 1rem;
    margin-top: auto;
  }
</style>
