<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { WeeklyFoodList, ConsumedFoods, FoodItem } from '../services/foodService';

  // Props
  export let weeklyFoodList: WeeklyFoodList;
  export let consumedFoods: ConsumedFoods = {};

  const dispatch = createEventDispatcher();

  // Toggle food consumed status
  function toggleFood(foodId: string) {
    dispatch('toggleFood', { foodId });

    // Update local state for immediate UI feedback
    consumedFoods = {
      ...consumedFoods,
      [foodId]: !consumedFoods[foodId]
    };
  }

  // Calculate total consumed count
  $: totalConsumed = Object.values(consumedFoods).filter(Boolean).length;

  // Calculate total food items
  $: totalItems = Object.values(weeklyFoodList).reduce((total, category) => {
    return total + (Array.isArray(category.coreItems) ? category.coreItems.length : 0);
  }, 0);

  // Calculate progress percentage
  $: progressPercentage = totalItems > 0 ? (totalConsumed / totalItems) * 100 : 0;
</script>

<div class="food-tracker">
  <div class="tracker-header">
    <h2>Weekly Food Tracker</h2>
    <p>Track your consumption of diverse healthy foods this week.</p>

    <div class="progress-bar-container">
      <div class="progress-bar">
        <div class="progress-fill" style="width: {progressPercentage}%"></div>
      </div>
      <div class="progress-text">
        {totalConsumed} of {totalItems} items consumed ({progressPercentage.toFixed(1)}%)
      </div>
    </div>
  </div>

  <div class="food-categories">
    {#each Object.entries(weeklyFoodList) as [category, categoryData]}
      <div class="category-card">
        <h3>{category.charAt(0).toUpperCase() + category.slice(1)}</h3>

        <div class="food-items">
          {#each categoryData.coreItems as item}
            <div
              class="food-item {consumedFoods[item.id] ? 'consumed' : ''}"
              on:click={() => toggleFood(item.id)}
              on:keydown={(e) => e.key === 'Enter' && toggleFood(item.id)}
              role="button"
              tabindex="0"
            >
              <div class="food-checkbox">
                {#if consumedFoods[item.id]}
                  <span class="checkmark">✓</span>
                {/if}
              </div>
              <div class="food-details">
                <span class="food-name">{item.name}</span>
                {#if item.servingSize}
                  <span class="serving-size">
                    {item.servingSize.metric} / {item.servingSize.imperial}
                  </span>
                {/if}
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/each}
  </div>
</div>

<style>
  .food-tracker {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
  }

  .tracker-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .progress-bar-container {
    margin: 1.5rem 0;
  }

  .progress-bar {
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background-color: #3498db;
    transition: width 0.3s ease;
  }

  .progress-text {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #666;
  }

  .food-categories {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .category-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
  }

  .category-card h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
  }

  .food-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .food-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .food-item:hover {
    background-color: #f9f9f9;
  }

  .food-item.consumed {
    background-color: #e8f7f0;
  }

  .food-checkbox {
    width: 24px;
    height: 24px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .food-item.consumed .food-checkbox {
    border-color: #2ecc71;
    background-color: #2ecc71;
    color: white;
  }

  .checkmark {
    font-weight: bold;
  }

  .food-details {
    flex: 1;
  }

  .food-name {
    display: block;
    font-weight: 500;
  }

  .serving-size {
    display: block;
    font-size: 0.8rem;
    color: #777;
    margin-top: 0.25rem;
  }

  @media (max-width: 768px) {
    .food-categories {
      grid-template-columns: 1fr;
    }
  }
</style>
