<script lang="ts">
  import { userStore, subscriptionTier } from '../stores/userStore';
  import { SubscriptionTiers } from '../models/userModel';
  
  // Subscribe to the subscription tier
  $: tier = $subscriptionTier;
  
  // Function to update subscription tier (for development/testing)
  function updateTier(newTier: SubscriptionTiers) {
    userStore.updateSubscriptionTier(newTier);
  }
</script>

<header class="header">
  <div class="header-container">
    <div class="logo">
      <h1>Food Diversity Tracker</h1>
    </div>
    
    <nav class="main-nav">
      <a href="#/" class="nav-link">Home</a>
      <a href="#/tracker" class="nav-link">Food Tracker</a>
      <a href="#/progress" class="nav-link">Progress</a>
      <a href="#/grocery-list" class="nav-link">Grocery List</a>
      <a href="#/export" class="nav-link">Export</a>
    </nav>
    
    <div class="subscription-badge">
      {#if tier === SubscriptionTiers.FREE}
        <span class="badge free">Free Tier</span>
      {:else if tier === SubscriptionTiers.PAID_TIER_1}
        <span class="badge premium">Premium</span>
      {:else if tier === SubscriptionTiers.PAID_TIER_2}
        <span class="badge premium-plus">Premium+</span>
      {/if}
    </div>
  </div>
</header>

<style>
  .header {
    background-color: #1A5276;
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
  
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  .logo h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: bold;
  }
  
  .main-nav {
    display: flex;
    gap: 1rem;
  }
  
  .nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.3s;
  }
  
  .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  .subscription-badge {
    margin-left: 1rem;
  }
  
  .badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
  }
  
  .free {
    background-color: #7FB3D5;
  }
  
  .premium {
    background-color: #D4AC0D;
  }
  
  .premium-plus {
    background-color: #7D6608;
  }
  
  @media (max-width: 768px) {
    .header-container {
      flex-direction: column;
      gap: 1rem;
    }
    
    .main-nav {
      flex-wrap: wrap;
      justify-content: center;
    }
  }
</style>
