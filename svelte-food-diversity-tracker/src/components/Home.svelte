<script lang="ts">
  // Home component for the landing page
</script>

<div class="home">
  <div class="hero">
    <h1>Track Your Food Diversity</h1>
    <p class="subtitle">Improve your health by consuming a variety of nutrient-dense foods</p>
    
    <div class="cta-buttons">
      <a href="#/tracker" class="btn btn-primary">Start Tracking</a>
      <a href="#/progress" class="btn btn-secondary">View Progress</a>
    </div>
  </div>
  
  <div class="features">
    <div class="feature-card">
      <div class="feature-icon">📋</div>
      <h3>Weekly Food Tracking</h3>
      <p>Track consumption of 30 core food items across different categories</p>
    </div>
    
    <div class="feature-card">
      <div class="feature-icon">📊</div>
      <h3>Progress Visualization</h3>
      <p>View your progress with engaging visual indicators</p>
    </div>
    
    <div class="feature-card">
      <div class="feature-icon">🛒</div>
      <h3>Grocery List Generation</h3>
      <p>Get a shopping list of foods you haven't consumed yet</p>
    </div>
    
    <div class="feature-card">
      <div class="feature-icon">📤</div>
      <h3>Export Functionality</h3>
      <p>Export your data in JSON, PDF, or Markdown formats</p>
    </div>
  </div>
  
  <div class="about-section">
    <h2>About Food Diversity Tracker</h2>
    <p>
      The Food Diversity Tracker is a Progressive Web Application that helps you track your weekly consumption of 30 different healthy food items based on LCHF/Mediterranean diet principles.
    </p>
    <p>
      Research shows that consuming a diverse range of foods provides a wider variety of nutrients, supports gut health, and may help prevent chronic diseases. Our app makes it easy to ensure you're getting this diversity in your diet.
    </p>
  </div>
</div>

<style>
  .home {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
  }
  
  .hero {
    text-align: center;
    padding: 3rem 1rem;
    margin-bottom: 2rem;
  }
  
  .hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #1A5276;
  }
  
  .subtitle {
    font-size: 1.2rem;
    color: #555;
    margin-bottom: 2rem;
  }
  
  .cta-buttons {
    margin: 2rem 0;
  }
  
  .btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    margin: 0 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    text-decoration: none;
    transition: background-color 0.3s;
  }
  
  .btn-primary {
    background-color: #3498db;
    color: white;
  }
  
  .btn-primary:hover {
    background-color: #2980b9;
  }
  
  .btn-secondary {
    background-color: #f7f9f9;
    color: #333;
    border: 1px solid #ddd;
  }
  
  .btn-secondary:hover {
    background-color: #eee;
  }
  
  .features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
  }
  
  .feature-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s;
  }
  
  .feature-card:hover {
    transform: translateY(-5px);
  }
  
  .feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }
  
  .feature-card h3 {
    margin-top: 0;
    color: #1A5276;
  }
  
  .about-section {
    background-color: #f7f9f9;
    padding: 2rem;
    border-radius: 8px;
    margin: 3rem 0;
  }
  
  .about-section h2 {
    color: #1A5276;
    margin-top: 0;
  }
  
  @media (max-width: 768px) {
    .hero h1 {
      font-size: 2rem;
    }
    
    .features {
      grid-template-columns: 1fr;
    }
  }
</style>
