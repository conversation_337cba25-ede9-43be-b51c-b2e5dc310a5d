<script lang="ts">
  const currentYear = new Date().getFullYear();
</script>

<footer class="footer">
  <div class="footer-container">
    <div class="footer-content">
      <div class="footer-section">
        <h3>Food Diversity Tracker</h3>
        <p>Track your consumption of 30 different healthy food items based on LCHF/Mediterranean diet principles.</p>
      </div>
      
      <div class="footer-section">
        <h3>Quick Links</h3>
        <ul>
          <li><a href="#/">Home</a></li>
          <li><a href="#/tracker">Food Tracker</a></li>
          <li><a href="#/progress">Progress</a></li>
          <li><a href="#/grocery-list">Grocery List</a></li>
        </ul>
      </div>
      
      <div class="footer-section">
        <h3>About</h3>
        <p>This application helps you maintain a diverse and healthy diet by tracking your consumption of various food groups.</p>
      </div>
    </div>
    
    <div class="footer-bottom">
      <p>&copy; {currentYear} Food Diversity Tracker. All rights reserved.</p>
    </div>
  </div>
</footer>

<style>
  .footer {
    background-color: #1A5276;
    color: white;
    padding: 2rem 0 1rem;
    margin-top: auto;
  }
  
  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  .footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 2rem;
    margin-bottom: 2rem;
  }
  
  .footer-section {
    flex: 1;
    min-width: 200px;
  }
  
  .footer-section h3 {
    margin-top: 0;
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }
  
  .footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .footer-section li {
    margin-bottom: 0.5rem;
  }
  
  .footer-section a {
    color: #7FB3D5;
    text-decoration: none;
    transition: color 0.3s;
  }
  
  .footer-section a:hover {
    color: white;
    text-decoration: underline;
  }
  
  .footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  @media (max-width: 768px) {
    .footer-content {
      flex-direction: column;
    }
  }
</style>
