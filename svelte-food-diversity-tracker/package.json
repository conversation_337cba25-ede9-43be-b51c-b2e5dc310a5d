{"name": "svelte-food-diversity-tracker", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.app.json && tsc -p tsconfig.node.json"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^5.0.3", "@tsconfig/svelte": "^5.0.4", "svelte": "^5.23.1", "svelte-check": "^4.1.5", "typescript": "~5.7.2", "vite": "^6.3.1"}, "dependencies": {"axios": "^1.9.0", "chart.js": "^4.4.9", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "svelte-spa-router": "^4.0.1"}}